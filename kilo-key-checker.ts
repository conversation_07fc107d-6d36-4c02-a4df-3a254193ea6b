import { Application, Router } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";

interface BalanceResult {
  key: string;
  success: boolean;
  balance?: string;
  error?: string;
}

interface TokenCheckResult {
  token: string;
  isValid: boolean;
  message: string;
  balance?: number;
}

interface APIProviderConfig {
  name: string;
  placeholder: string;
  hasBalance: boolean;
  checkFunction: string;
}

// API 提供商配置
const API_PROVIDERS: Record<string, APIProviderConfig> = {
  openai: {
    name: "OpenAI 通用",
    placeholder: "请输入 OpenAI API KEY，多个以英文逗号、分号或换行分隔\\n\\n示例格式：\\nsk-xxx1,sk-xxx2\\nsk-xxx3;sk-xxx4\\nsk-xxx5",
    hasBalance: false,
    checkFunction: "checkOpenAIToken"
  },
  silicoflow: {
    name: "硅基流动",
    placeholder: "请输入硅基流动 API KEY，多个以英文逗号、分号或换行分隔\\n\\n示例格式：\\nsk-xxx1,sk-xxx2\\nsk-xxx3;sk-xxx4\\nsk-xxx5",
    hasBalance: true,
    checkFunction: "checkSilicoFlowToken"
  },
  gemini: {
    name: "Google Gemini",
    placeholder: "请输入 Google Gemini API KEY，多个以英文逗号、分号或换行分隔\\n\\n示例格式：\\nAIzaSyXXX1,AIzaSyXXX2\\nAIzaSyXXX3;AIzaSyXXX4\\nAIzaSyXXX5",
    hasBalance: false,
    checkFunction: "checkGeminiToken"
  },
  kilokey: {
    name: "Kilo Key",
    placeholder: "请输入 Kilo Key API TOKEN，多个以英文逗号、分号或换行分隔\\n\\n示例格式：\\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    hasBalance: true,
    checkFunction: "checkKiloKeyToken"
  }
};

const app = new Application();
const router = new Router();

// 启用CORS
app.use(oakCors({
  origin: "*",
  methods: ["GET", "POST", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
}));

// 静态文件服务 - 主页面
app.use(async (ctx, next) => {
  if (ctx.request.url.pathname === '/') {
    ctx.response.body = getMainPageHTML();
    ctx.response.type = 'text/html';
  } else if (ctx.request.url.pathname === '/kilo') {
    ctx.response.body = getKiloPageHTML();
    ctx.response.type = 'text/html';
  } else {
    await next();
  }
});

// API路由：检查Kilo Key余额
router.post('/api/check-balance', async (ctx) => {
  try {
    const body = await ctx.request.body().value;
    const { keys } = body;

    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      ctx.response.status = 400;
      ctx.response.body = { error: '请提供有效的API密钥数组' };
      return;
    }

    const results: BalanceResult[] = [];

    for (const key of keys) {
      try {
        const balance = await checkKiloKeyBalance(key);
        results.push({
          key: key,
          success: true,
          balance: balance,
        });
      } catch (error) {
        results.push({
          key: key,
          success: false,
          error: error.message,
        });
      }
    }

    ctx.response.body = { results };
  } catch (error) {
    ctx.response.status = 500;
    ctx.response.body = { error: '服务器内部错误' };
  }
});

// API路由：通用API KEY检测
router.post('/api/check-tokens', async (ctx) => {
  try {
    const body = await ctx.request.body().value;
    const { provider, tokens, threshold = 1, concurrency = 5 } = body;

    if (!provider || !tokens || !Array.isArray(tokens)) {
      ctx.response.status = 400;
      ctx.response.body = { error: '请提供有效的参数' };
      return;
    }

    const results: TokenCheckResult[] = [];

    // 并发检测
    const semaphore = new Array(concurrency).fill(null);
    const promises = tokens.map(async (token: string) => {
      // 等待信号量
      await new Promise(resolve => {
        const tryAcquire = () => {
          const index = semaphore.findIndex(slot => slot === null);
          if (index !== -1) {
            semaphore[index] = token;
            resolve(index);
          } else {
            setTimeout(tryAcquire, 10);
          }
        };
        tryAcquire();
      });

      try {
        let result: TokenCheckResult;
        switch (provider) {
          case 'openai':
            result = await checkOpenAIToken(token);
            break;
          case 'silicoflow':
            result = await checkSilicoFlowToken(token);
            break;
          case 'gemini':
            result = await checkGeminiToken(token);
            break;
          case 'kilokey':
            result = await checkKiloKeyToken(token);
            break;
          default:
            result = { token, isValid: false, message: "未知提供商" };
        }
        results.push(result);
      } catch (error) {
        results.push({
          token,
          isValid: false,
          message: `检测失败: ${error.message}`
        });
      } finally {
        // 释放信号量
        const index = semaphore.findIndex(slot => slot === token);
        if (index !== -1) {
          semaphore[index] = null;
        }
      }
    });

    await Promise.all(promises);
    ctx.response.body = { results };
  } catch (error) {
    ctx.response.status = 500;
    ctx.response.body = { error: '服务器内部错误' };
  }
});

// 检查Kilo Key余额
async function checkKiloKeyBalance(apiKey: string): Promise<string> {
  const url = 'https://kilocode.ai/api/profile/balance';
  
  const headers = new Headers();
  headers.append('Authorization', `Bearer ${apiKey}`);
  headers.append('Content-Type', 'application/json');
  headers.append('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
  headers.append('Accept', 'application/json');
  headers.append('Referer', 'https://kilocode.ai/profile');

  const response = await fetch(url, {
    method: 'GET',
    headers: headers,
  });

  if (response.status === 200) {
    const data = await response.json();
    if (data.balance !== undefined) {
      return data.balance.toString();
    } else {
      throw new Error('返回数据中未找到余额信息');
    }
  } else if (response.status === 401) {
    throw new Error('API密钥无效');
  } else if (response.status === 404) {
    throw new Error('API端点不存在');
  } else {
    const errorText = await response.text();
    throw new Error(`请求失败，状态码: ${response.status}, 错误信息: ${errorText}`);
  }
}

// Kilo Key TOKEN检测
async function checkKiloKeyToken(token: string): Promise<TokenCheckResult> {
  try {
    const balance = await checkKiloKeyBalance(token);
    return {
      token: token,
      isValid: true,
      message: "有效",
      balance: parseFloat(balance)
    };
  } catch (error) {
    return {
      token: token,
      isValid: false,
      message: error.message
    };
  }
}

// OpenAI 通用TOKEN检测
async function checkOpenAIToken(token: string): Promise<TokenCheckResult> {
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) {
      return {
        token: token,
        isValid: true,
        message: "有效"
      };
    } else {
      const errorData = await response.json().catch(() => null);
      let message = "无效";
      
      if (response.status === 429) {
        message = "429 - 请求过于频繁";
      } else if (response.status === 401) {
        message = "401 - 认证失败";
      } else if (response.status === 403) {
        message = "403 - 权限不足";
      } else if (errorData && errorData.error && errorData.error.message) {
        message = errorData.error.message;
      }

      return {
        token: token,
        isValid: false,
        message: message
      };
    }
  } catch (error) {
    return {
      token: token,
      isValid: false,
      message: "网络错误: " + error.message
    };
  }
}

// 硅基流动TOKEN检测
async function checkSilicoFlowToken(token: string): Promise<TokenCheckResult> {
  try {
    // 1. 验证token
    const resp1 = await fetch("https://api.siliconflow.cn/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": "Bearer " + token,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        model: "Qwen/Qwen2.5-7B-Instruct",
        messages: [{ role: "user", content: "hi" }],
        max_tokens: 100,
        stream: false
      })
    });

    if (!resp1.ok) {
      const errData = await resp1.json().catch(() => null);
      return {
        token,
        isValid: false,
        message: errData && errData.message ? errData.message : "验证失败"
      };
    }

    // 2. 查询余额
    const resp2 = await fetch("https://api.siliconflow.cn/v1/user/info", {
      method: "GET",
      headers: { "Authorization": "Bearer " + token }
    });

    if (!resp2.ok) {
      return {
        token,
        isValid: true,
        balance: -1,
        message: "有效但无法获取余额"
      };
    }

    const data2 = await resp2.json();
    const balance = data2.data && data2.data.balance !== undefined ? data2.data.balance : -1;

    return {
      token,
      isValid: true,
      balance: balance,
      message: "有效"
    };
  } catch (error) {
    return {
      token: token,
      isValid: false,
      message: "网络错误: " + error.message
    };
  }
}

// Google Gemini TOKEN检测
async function checkGeminiToken(token: string): Promise<TokenCheckResult> {
  try {
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-8b:generateContent?key=${token}`;

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: "Hello"
          }]
        }],
        generationConfig: {
          maxOutputTokens: 1
        }
      })
    });

    if (response.ok) {
      return {
        token: token,
        isValid: true,
        message: "有效"
      };
    } else {
      const errorData = await response.json().catch(() => null);
      let message = "无效";
      
      if (response.status === 429) {
        message = "429 - 请求过于频繁";
      } else if (response.status === 400) {
        message = "400 - API KEY无效";
      } else if (response.status === 403) {
        message = "403 - API KEY被禁用或权限不足";
      } else if (errorData && errorData.error && errorData.error.message) {
        message = errorData.error.message;
      }

      return {
        token: token,
        isValid: false,
        message: message
      };
    }
  } catch (error) {
    return {
      token: token,
      isValid: false,
      message: "网络错误: " + error.message
    };
  }
}

// 获取主页面HTML
function getMainPageHTML(): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>API KEY检测工具</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      color: #2c3e50;
      line-height: 1.6;
      padding: 16px;
      min-height: 100vh;
      margin: 0;
    }

    .container {
      max-width: 900px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.98);
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 20px rgba(0, 0, 0, 0.1);
      padding: 40px;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header {
      text-align: center;
      margin-bottom: 32px;
    }

    .logo {
      font-size: 72px;
      margin-bottom: 20px;
      filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
      background: linear-gradient(135deg, #4facfe, #00f2fe);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    h1 {
      font-size: 2.5rem;
      background: linear-gradient(135deg, #2e7d32, #4facfe);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 12px;
      font-weight: 700;
    }

    .subtitle {
      color: #64748b;
      font-size: 1.1rem;
      font-weight: 500;
    }

    .nav-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-bottom: 32px;
    }

    .nav-btn {
      padding: 12px 24px;
      background: linear-gradient(135deg, #4facfe, #00f2fe);
      color: white;
      text-decoration: none;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    }

    .nav-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
    }

    .input-section {
      margin-bottom: 28px;
      background: linear-gradient(135deg, #f8fafc, #f1f5f9);
      padding: 24px;
      border-radius: 16px;
      border: 1px solid #e2e8f0;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }

    .provider-options {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      margin-top: 8px;
    }

    .radio-option {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      background: white;
      transition: all 0.3s ease;
      flex: 1;
      min-width: 140px;
    }

    .radio-option:hover {
      border-color: #4facfe;
      background: #f8fafc;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(79, 172, 254, 0.1);
    }

    .radio-option input[type="radio"] {
      display: none;
    }

    .radio-custom {
      width: 20px;
      height: 20px;
      border: 2px solid #cbd5e1;
      border-radius: 50%;
      position: relative;
      transition: all 0.3s ease;
    }

    .radio-custom::after {
      content: '';
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #4facfe;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0);
      transition: transform 0.2s ease;
    }

    .radio-option input[type="radio"]:checked + .radio-custom {
      border-color: #4facfe;
      background: #e3f2fd;
    }

    .radio-option input[type="radio"]:checked + .radio-custom::after {
      transform: translate(-50%, -50%) scale(1);
    }

    .radio-option input[type="radio"]:checked ~ .radio-label {
      color: #4facfe;
      font-weight: 600;
    }

    .radio-label {
      font-size: 0.95rem;
      font-weight: 500;
      color: #374151;
      transition: all 0.3s ease;
    }

    label {
      display: block;
      margin-bottom: 12px;
      font-weight: 600;
      color: #374151;
      font-size: 1rem;
    }

    textarea {
      width: 100%;
      padding: 16px;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      font-size: 15px;
      transition: all 0.3s ease;
      background: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
      height: 180px;
      resize: vertical;
      font-family: monospace;
      line-height: 1.5;
    }

    textarea:focus {
      border-color: #4facfe;
      box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
      outline: none;
      transform: translateY(-1px);
    }

    .button {
      width: 100%;
      height: 56px;
      font-size: 1.1rem;
      letter-spacing: 0.5px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 16px 32px;
      border-radius: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: none;
      color: white;
      gap: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      position: relative;
      overflow: hidden;
      background: linear-gradient(135deg, #4facfe, #00f2fe);
    }

    .button:hover {
      background: linear-gradient(135deg, #3b9cfd, #00d4fe);
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
    }

    .button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .results-section {
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 16px;
      border: 1px solid #e2e8f0;
      display: none;
    }

    .results-content {
      background: #f8fafc;
      padding: 12px;
      border-radius: 8px;
      font-family: monospace;
      font-size: 0.9rem;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
    }

    .footer {
      text-align: center;
      margin-top: 32px;
      padding-top: 16px;
      border-top: 1px solid #e2e8f0;
      color: #666;
      font-size: 0.9rem;
    }

    .loader {
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #4facfe;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🔑</div>
      <h1>API KEY检测工具</h1>
      <p class="subtitle">支持多种AI服务的API KEY有效性检测</p>
    </div>

    <div class="nav-buttons">
      <a href="/" class="nav-btn">通用检测</a>
      <a href="/kilo" class="nav-btn">Kilo Key专用</a>
    </div>

    <div class="input-section">
      <label>API 提供商</label>
      <div class="provider-options">
        <label class="radio-option">
          <input type="radio" name="provider" value="openai" checked>
          <span class="radio-custom"></span>
          <span class="radio-label">OpenAI 通用</span>
        </label>
        <label class="radio-option">
          <input type="radio" name="provider" value="silicoflow">
          <span class="radio-custom"></span>
          <span class="radio-label">硅基流动</span>
        </label>
        <label class="radio-option">
          <input type="radio" name="provider" value="gemini">
          <span class="radio-custom"></span>
          <span class="radio-label">Google Gemini</span>
        </label>
        <label class="radio-option">
          <input type="radio" name="provider" value="kilokey">
          <span class="radio-custom"></span>
          <span class="radio-label">Kilo Key</span>
        </label>
      </div>
    </div>

    <div class="input-section">
      <label for="tokens">API KEYS</label>
      <textarea
        id="tokens"
        placeholder="请输入 API KEY，多个以英文逗号、分号或换行分隔"
      ></textarea>
    </div>

    <div class="input-section">
      <button id="checkButton" class="button">
        开始检测KEY
      </button>
    </div>

    <div class="results-section" id="results">
      <h3>检测结果</h3>
      <div class="results-content" id="resultsContent"></div>
    </div>

    <div class="footer">
      <p>© 2025 API KEY 检测工具 | 支持多种AI服务提供商</p>
    </div>
  </div>

  <script>
    const API_PROVIDERS = ${JSON.stringify(API_PROVIDERS, null, 2)};

    document.getElementById('checkButton').addEventListener('click', async function() {
      const tokensTextarea = document.getElementById('tokens');
      const checkButton = document.getElementById('checkButton');
      const resultsSection = document.getElementById('results');
      const resultsContent = document.getElementById('resultsContent');

      const tokensInput = tokensTextarea.value.trim();
      if (!tokensInput) {
        alert('请输入至少一个 API KEY');
        return;
      }

      const provider = document.querySelector('input[name="provider"]:checked').value;
      
      // 分割tokens
      let tokens = tokensInput
        .split(new RegExp('[,;\\\\s\\\\n\\\\r]+'))
        .map(t => t.trim())
        .filter(t => t !== "");

      if (tokens.length === 0) {
        alert('请输入有效的 API KEY');
        return;
      }

      // 显示加载状态
      checkButton.disabled = true;
      checkButton.innerHTML = '<span class="loader"></span>检测中...';
      resultsContent.textContent = '';
      resultsSection.style.display = 'none';

      try {
        const response = await fetch('/api/check-tokens', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            provider: provider,
            tokens: tokens,
            threshold: 1,
            concurrency: 5
          }),
        });

        const data = await response.json();
        
        if (response.ok) {
          displayResults(data.results);
        } else {
          resultsContent.textContent = '检测失败: ' + (data.error || '未知错误');
          resultsSection.style.display = 'block';
        }
      } catch (error) {
        resultsContent.textContent = '网络错误: ' + error.message;
        resultsSection.style.display = 'block';
      } finally {
        checkButton.disabled = false;
        checkButton.textContent = '开始检测KEY';
      }
    });

    function displayResults(results) {
      const resultsContent = document.getElementById('resultsContent');
      const resultsSection = document.getElementById('results');
      
      let output = '';
      let validCount = 0;
      let invalidCount = 0;

      results.forEach((result, index) => {
        if (result.isValid) {
          validCount++;
          const balanceInfo = result.balance !== undefined ? \` (余额: \${result.balance})\` : '';
          output += \`✅ \${result.token}\${balanceInfo} - \${result.message}\\n\`;
        } else {
          invalidCount++;
          output += \`❌ \${result.token} - \${result.message}\\n\`;
        }
      });

      output = \`检测完成！\\n有效: \${validCount} 个，无效: \${invalidCount} 个\\n\\n\` + output;
      
      resultsContent.textContent = output;
      resultsSection.style.display = 'block';
    }

    // 初始化placeholder
    function updatePlaceholder() {
      const provider = document.querySelector('input[name="provider"]:checked').value;
      const tokensTextarea = document.getElementById('tokens');
      const config = API_PROVIDERS[provider];
      if (config) {
        tokensTextarea.placeholder = config.placeholder;
      }
    }

    // 绑定provider选择器的事件处理器
    const providerRadios = document.querySelectorAll('input[name="provider"]');
    providerRadios.forEach(radio => {
      radio.addEventListener('change', updatePlaceholder);
    });

    // 初始化placeholder
    updatePlaceholder();
  </script>
</body>
</html>`;
}

// 获取Kilo Key专用页面HTML
function getKiloPageHTML(): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kilo Key 余额查询器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .header {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 40px 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .nav-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .nav-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .input-group {
            margin-bottom: 30px;
        }
        
        label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: #555;
            font-size: 1.1rem;
        }
        
        textarea {
            width: 100%;
            padding: 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            resize: both;
            min-height: 120px;
            max-height: 400px;
            box-sizing: border-box;
            transition: all 0.3s ease;
            font-family: 'Consolas', 'Monaco', monospace;
            background-color: #f8f9fa;
        }
        
        textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            background-color: white;
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 16px 40px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        button:disabled {
            background: linear-gradient(135deg, #cccccc 0%, #999999 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: rgba(79, 172, 254, 0.1);
            border-radius: 12px;
        }
        
        .spinner {
            border: 4px solid rgba(79, 172, 254, 0.2);
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            padding: 16px 20px;
            border-radius: 12px;
            margin: 20px 0;
            display: none;
            font-weight: 500;
            align-items: center;
            justify-content: space-between;
        }
        
        .error {
            background-color: #fee;
            color: #c33;
            border-left: 4px solid #c33;
        }
        
        .success {
            background-color: #efe;
            color: #3c3;
            border-left: 4px solid #3c3;
        }
        
        .result-container {
            margin-top: 40px;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .result-header h2 {
            color: #333;
            font-size: 1.8rem;
            margin-right: 15px;
        }
        
        .result-count {
            display: none;
        }
        
        .result-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        
        .result-table th, .result-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .result-table th {
            background-color: #e8f5e9;
            color: #2e7d32;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #2e7d32;
        }
        
        .result-table tr:last-child td {
            border-bottom: none;
        }
        
        .result-table tr:nth-child(even) {
            background-color: rgba(79, 172, 254, 0.05);
        }
        
        .result-table tr:hover {
            background-color: rgba(79, 172, 254, 0.1);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }
        
        .key-preview {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            color: #666;
            background-color: #f5f5f5;
            padding: 8px 12px;
            border-radius: 6px;
            display: inline-block;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            user-select: none;
        }
        
        .key-preview:hover {
            background-color: #e9ecef;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .key-preview:active {
            transform: translateY(0);
        }
        
        .copy-tooltip {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }
        
        .key-preview:hover .copy-tooltip {
            opacity: 1;
        }
        
        .copy-success {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }
        
        .key-preview.copied .copy-success {
            opacity: 1;
        }
        
        .balance {
            font-weight: 700;
            color: #4CAF50;
            font-size: 1.2rem;
        }
        
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .help-text {
            font-size: 14px;
            color: #666;
            margin-top: 8px;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #4facfe;
        }
        
        .icon {
            margin-right: 8px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .result-table {
                font-size: 14px;
            }
            
            .result-table th, .result-table td {
                padding: 12px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="icon">💰</span> Kilo Key 余额查询器</h1>
            <div class="subtitle">批量查询您的API密钥余额</div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn">通用检测</a>
                <a href="/kilo" class="nav-btn">Kilo Key专用</a>
            </div>
        </div>
        
        <div class="content">
            <div class="input-group">
                <label for="apiKeys"><span class="icon">🔑</span> API 密钥 (逗号分隔):</label>
                <textarea id="apiKeys" placeholder="请输入API密钥，多个密钥用逗号分隔..."></textarea>
                <div class="help-text">
                    <strong>使用说明:</strong><br>
                    • 每个密钥应该是完整的JWT token<br>
                    • 多个密钥请用英文逗号分隔<br>
                    • 示例: key1,key2,key3
                </div>
            </div>
            
            <div class="button-container">
                <button id="checkBalance"><span class="icon">🔍</span> 查询余额</button>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p><strong>正在查询余额，请稍候...</strong></p>
            </div>
            
            <div class="message error" id="errorMessage">
                <span></span>
                <span>✕</span>
            </div>
            
            <div class="message success" id="successMessage">
                <span></span>
                <span>✓</span>
            </div>
            
            <div class="result-container" id="resultContainer" style="display: none;">
                <div class="result-header">
                    <h2><span class="icon">📊</span> 查询结果</h2>
                    <div class="result-count" id="resultCount">0 个密钥</div>
                </div>
                <table class="result-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>API 密钥 (预览)</th>
                            <th>余额</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="resultTableBody">
                        <!-- 结果将在这里动态插入 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('checkBalance').addEventListener('click', async function() {
            const apiKeys = document.getElementById('apiKeys').value.trim();
            
            if (!apiKeys) {
                showError('请输入至少一个API密钥');
                return;
            }
            
            // 分割API密钥
            const keys = apiKeys.split(',').map(key => key.trim()).filter(key => key);
            
            if (keys.length === 0) {
                showError('请输入有效的API密钥');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultContainer').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('checkBalance').disabled = true;
            
            try {
                // 发送请求到后端API
                const response = await fetch('/api/check-balance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ keys: keys }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 显示结果
                    displayResults(data.results);
                    showSuccess(\`成功查询 \${data.results.length} 个API密钥的余额\`);
                } else {
                    showError(data.error || '查询失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            } finally {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                document.getElementById('checkBalance').disabled = false;
            }
        });
        
        function displayResults(results) {
            const tableBody = document.getElementById('resultTableBody');
            tableBody.innerHTML = '';
            
            // 更新结果计数
            document.getElementById('resultCount').textContent = results.length + ' 个密钥';
            
            results.forEach((result, index) => {
                const row = document.createElement('tr');
                
                // 序号
                const indexCell = document.createElement('td');
                indexCell.textContent = index + 1;
                row.appendChild(indexCell);
                
                // API密钥预览
                const keyCell = document.createElement('td');
                const keyPreview = document.createElement('div');
                keyPreview.className = 'key-preview';
                
                // 添加提示文本
                const tooltip = document.createElement('div');
                tooltip.className = 'copy-tooltip';
                tooltip.textContent = '点击复制';
                keyPreview.appendChild(tooltip);
                
                // 添加成功提示
                const successMsg = document.createElement('div');
                successMsg.className = 'copy-success';
                successMsg.textContent = '已复制!';
                keyPreview.appendChild(successMsg);
                
                // 设置密钥预览文本
                const keyText = document.createElement('span');
                keyText.textContent = result.key.length > 30
                    ? result.key.substring(0, 15) + '...' + result.key.substring(result.key.length - 15)
                    : result.key;
                keyPreview.appendChild(keyText);
                
                // 添加点击复制功能
                keyPreview.addEventListener('click', function() {
                    navigator.clipboard.writeText(result.key).then(function() {
                        keyPreview.classList.add('copied');
                        setTimeout(function() {
                            keyPreview.classList.remove('copied');
                        }, 2000);
                    }).catch(function(err) {
                        console.error('复制失败:', err);
                    });
                });
                
                keyCell.appendChild(keyPreview);
                row.appendChild(keyCell);
                
                // 余额
                const balanceCell = document.createElement('td');
                if (result.success) {
                    const balance = document.createElement('span');
                    balance.className = 'balance';
                    balance.textContent = result.balance;
                    balanceCell.appendChild(balance);
                } else {
                    balanceCell.textContent = '-';
                }
                row.appendChild(balanceCell);
                
                // 状态
                const statusCell = document.createElement('td');
                const status = document.createElement('span');
                if (result.success) {
                    status.className = 'status success';
                    status.textContent = '成功';
                } else {
                    status.className = 'status error';
                    status.textContent = '失败';
                }
                statusCell.appendChild(status);
                row.appendChild(statusCell);
                
                tableBody.appendChild(row);
            });
            
            document.getElementById('resultContainer').style.display = 'block';
        }
        
        function showError(message) {
            const errorElement = document.getElementById('errorMessage');
            const messageSpan = errorElement.querySelector('span:first-child');
            messageSpan.textContent = message;
            errorElement.style.display = 'flex';
        }
        
        function showSuccess(message) {
            const successElement = document.getElementById('successMessage');
            const messageSpan = successElement.querySelector('span:first-child');
            messageSpan.textContent = message;
            successElement.style.display = 'flex';
        }
    </script>
</body>
</html>`;
}

// 注册路由
app.use(router.routes());
app.use(router.allowedMethods());

// 启动服务器
const port = 8000;
console.log(`🔑 API KEY检测工具服务器启动中...`);
console.log(`🌐 通用检测页面: http://localhost:${port}`);
console.log(`💰 Kilo Key专用页面: http://localhost:${port}/kilo`);

if (import.meta.main) {
  await app.listen({ port });
}