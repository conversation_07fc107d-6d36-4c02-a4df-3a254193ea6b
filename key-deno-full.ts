import { serve } from "https://deno.land/std@0.220.0/http/server.ts";

// 类型定义
interface APIProviderConfig {
  label: string;
  icon: string;
  hasBalance: boolean;
  defaultBase: string;
  defaultModel: string;
  checkFunction: string;
  fetchModels: string;
}

interface TokenCheckResult {
  token: string;
  isValid: boolean;
  message?: string;
  balance?: number;
  totalBalance?: number;
  usedBalance?: number;
  rawError?: any;
  error?: boolean;
}

// API 提供商配置
const PROVIDERS: Record<string, APIProviderConfig> = {
  openai: {
    label: 'OpenAI',
    icon: '🤖',
    hasBalance: false,
    defaultBase: 'https://api-proxy.me/openai/v1',
    defaultModel: 'gpt-4o-mini',
    checkFunction: 'checkOpenAIToken',
    fetchModels: 'fetchOpenAIModels'
  },
  anthropic: {
    label: 'Anthropic',
    icon: '🔮',
    hasBalance: false,
    defaultBase: 'https://api-proxy.me/anthropic/v1',
    defaultModel: 'claude-3-5-haiku-20241022',
    checkFunction: 'checkAnthropicToken',
    fetchModels: 'fetchAnthropicModels'
  },
  gemini: {
    label: 'Google Gemini',
    icon: '✨',
    hasBalance: false,
    defaultBase: 'https://api-proxy.me/gemini',
    defaultModel: 'gemini-2.5-flash',
    checkFunction: 'checkGeminiToken',
    fetchModels: 'fetchGoogleModels'
  },
  xai: {
    label: 'X AI',
    icon: '🚀',
    hasBalance: false,
    defaultBase: 'https://api-proxy.me/xai/v1',
    defaultModel: 'grok-3-latest',
    checkFunction: 'checkXaiToken',
    fetchModels: 'fetchXAIModels'
  },
  openrouter: {
    label: 'OpenRouter',
    icon: '🌐',
    hasBalance: true,
    defaultBase: 'https://api-proxy.me/openrouter/v1',
    defaultModel: 'mistralai/mistral-7b-instruct:free',
    checkFunction: 'checkOpenRouterToken',
    fetchModels: 'fetchOpenRouterModels'
  },
  groq: {
    label: 'Groq',
    icon: '🥗',
    hasBalance: false,
    defaultBase: 'https://api-proxy.me/groq/v1',
    defaultModel: 'openai/gpt-oss-20b',
    checkFunction: 'checkGroqToken',
    fetchModels: 'fetchGroqModels'
  },
  github: {
    label: 'GitHub Models',
    icon: '🐱',
    hasBalance: false,
    defaultBase: 'https://models.github.ai/inference',
    defaultModel: 'gpt-4o-mini',
    checkFunction: 'checkGitHubToken',
    fetchModels: 'fetchGitHubModels'
  },
  siliconflow: {
    label: 'SiliconFlow',
    icon: '💧',
    hasBalance: true,
    defaultBase: 'https://api.siliconflow.cn/v1',
    defaultModel: 'Qwen/Qwen2.5-7B-Instruct',
    checkFunction: 'checkSiliconFlowToken',
    fetchModels: 'fetchSiliconFlowModels'
  },
  deepseek: {
    label: 'DeepSeek',
    icon: '🔍',
    hasBalance: true,
    defaultBase: 'https://api.deepseek.com/v1',
    defaultModel: 'deepseek-chat',
    checkFunction: 'checkDeepSeekToken',
    fetchModels: 'fetchDeepSeekModels'
  },
  moonshot: {
    label: 'Moonshot',
    icon: '🌙',
    hasBalance: true,
    defaultBase: 'https://api.moonshot.cn/v1',
    defaultModel: 'kimi-latest',
    checkFunction: 'checkMoonshotToken',
    fetchModels: 'fetchMoonshotModels'
  },
  aliyun: {
    label: '阿里云',
    icon: '☁️',
    hasBalance: false,
    defaultBase: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    defaultModel: 'qwen-turbo',
    checkFunction: 'checkAliyunToken',
    fetchModels: 'fetchAliyunModels'
  },
  zhipu: {
    label: '智谱',
    icon: '〽️',
    hasBalance: false,
    defaultBase: 'https://open.bigmodel.cn/api/paas/v4',
    defaultModel: 'glm-4.5-air',
    checkFunction: 'checkZhipuToken',
    fetchModels: 'fetchZhipuModels'
  },
  kilo: {
    label: 'Kilo Code',
    icon: '🔥',
    hasBalance: true,
    defaultBase: 'https://kilocode.ai/api',
    defaultModel: 'gpt-4o-mini',
    checkFunction: 'checkKiloToken',
    fetchModels: 'fetchKiloModels'
  },
  nebius: {
    label: 'Nebius',
    icon: '🌌',
    hasBalance: true,
    defaultBase: 'https://api.studio.nebius.ai/v1',
    defaultModel: 'deepseek-ai/DeepSeek-V3-0324',
    checkFunction: 'checkNebiusToken',
    fetchModels: 'fetchNebiusModels'
  },
  gmicloud: {
    label: 'GMI Cloud',
    icon: '☁️',
    hasBalance: false,
    defaultBase: 'https://api.gmi-serving.com/v1',
    defaultModel: 'deepseek-chat',
    checkFunction: 'checkGMICloudToken',
    fetchModels: 'fetchGMICloudModels'
  }
};

// API 错误处理
async function handleApiError(response: Response): Promise<{ message: string; rawError: any }> {
  const rawText = await response.text();
  let rawError: any;
  try {
    rawError = JSON.parse(rawText);
  } catch (e) {
    rawError = rawText;
  }

  let message = `HTTP ${response.status}`;
  if (typeof rawError === 'object' && rawError?.error?.message) {
    message = rawError.error.message;
  } else if (typeof rawError === 'object' && rawError?.message) {
    message = rawError.message;
  } else if (typeof rawError === 'string' && rawError.length > 0 && rawError.length < 100) {
    message = rawError;
  } else if (response.status === 401) {
    message = '认证失败';
  } else if (response.status === 429) {
    message = '请求过于频繁';
  } else if (response.status === 400) {
    message = '请求无效';
  }
  return { message, rawError };
}

// 代理请求处理
async function handleProxyRequest(request: Request): Promise<Response> {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key, anthropic-version, anthropic-dangerous-direct-browser-access',
        'Access-Control-Max-Age': '86400',
      }
    });
  }

  if (request.method !== 'POST') {
    return new Response('Method Not Allowed', { status: 405 });
  }

  let payload: any;
  try {
    payload = await request.json();
  } catch (_) {
    return new Response('Invalid JSON', { status: 400 });
  }

  const { targetUrl, method = 'GET', headers, body } = payload || {};
  if (!targetUrl) {
    return new Response('Target URL is required', { status: 400 });
  }

  const init: RequestInit = { method };
  if (headers && typeof headers === 'object') init.headers = headers;
  if (body !== undefined && !['GET', 'HEAD'].includes(method)) init.body = body;

  let upstream: Response;
  try {
    upstream = await fetch(targetUrl, init);
  } catch (err) {
    return new Response((err as Error).message, { status: 502 });
  }

  const newHeaders = new Headers(upstream.headers);
  newHeaders.set('Access-Control-Allow-Origin', '*');
  newHeaders.set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  newHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-api-key, anthropic-version, anthropic-dangerous-direct-browser-access');

  return new Response(upstream.body, {
    status: upstream.status,
    statusText: upstream.statusText,
    headers: newHeaders
  });
}

// Token 检测函数
async function checkOpenAIToken(token: string, baseUrl = 'https://api.openai.com/v1', testModel = 'gpt-4o-mini'): Promise<TokenCheckResult> {
  try {
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    
    const commonPayload = { model: testModel, messages: [{ role: "user", content: "Hello" }] };
    const headers = { "Content-Type": "application/json", "Authorization": "Bearer " + token };

    const response1 = await fetch(apiUrl, { 
      method: "POST", 
      headers, 
      body: JSON.stringify({ ...commonPayload, max_tokens: 1 }) 
    });

    if (response1.ok) return { token, isValid: true };

    const { message: message1, rawError: rawError1 } = await handleApiError(response1);

    if (rawError1?.error?.code === 'unsupported_parameter' && rawError1?.error?.param === 'max_tokens') {
      const response2 = await fetch(apiUrl, { 
        method: "POST", 
        headers, 
        body: JSON.stringify({ ...commonPayload, max_completion_tokens: 10 }) 
      });
      
      if (response2.ok) return { token, isValid: true };
      
      const { message: message2, rawError: rawError2 } = await handleApiError(response2);
      return { token, isValid: false, message: message2, rawError: rawError2, error: true };
    }

    return { token, isValid: false, message: message1, rawError: rawError1, error: true };
  } catch (error) { 
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true }; 
  }
}

async function checkAnthropicToken(token: string, baseUrl = 'https://api.anthropic.com/v1', testModel = 'claude-3-5-haiku-20241022'): Promise<TokenCheckResult> {
  try {
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/messages';
    const headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer " + token,
      "anthropic-version": "2023-06-01",
      "anthropic-dangerous-direct-browser-access": "true"
    };

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        model: testModel,
        max_tokens: 1,
        messages: [{ role: "user", content: "Hello" }]
      })
    });

    if (response.ok) return { token, isValid: true };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkGeminiToken(token: string, baseUrl = 'https://generativelanguage.googleapis.com/v1beta', testModel = 'gemini-2.5-flash'): Promise<TokenCheckResult> {
  try {
    const apiUrl = `${baseUrl.replace(/\/+$/, '')}/models/${testModel}:generateContent?key=${token}`;
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        contents: [{ parts: [{ text: "Hello" }] }],
        generationConfig: { maxOutputTokens: 1 }
      })
    });

    if (response.ok) return { token, isValid: true };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkXaiToken(token: string, baseUrl = 'https://api.x.ai/v1', testModel = 'grok-3-latest'): Promise<TokenCheckResult> {
  try {
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkOpenRouterToken(token: string, baseUrl = 'https://openrouter.ai/api/v1', testModel = 'mistralai/mistral-7b-instruct:free'): Promise<TokenCheckResult> {
  try {
    // 先检查余额
    const balanceUrl = baseUrl.replace(/\/+$/, '') + '/auth/key';
    const balanceResponse = await fetch(balanceUrl, {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (balanceResponse.ok) {
      const balanceData = await balanceResponse.json();
      const balance = balanceData.data?.limit_remaining || -1;
      return { token, isValid: true, balance, message: "有效" };
    }

    // 如果余额检查失败，尝试API调用
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true, balance: -1 };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkGroqToken(token: string, baseUrl = 'https://api.groq.com/openai/v1', testModel = 'openai/gpt-oss-20b'): Promise<TokenCheckResult> {
  try {
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkGitHubToken(token: string, baseUrl = 'https://models.github.ai/inference', testModel = 'gpt-4o-mini'): Promise<TokenCheckResult> {
  try {
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkSiliconFlowToken(token: string, baseUrl = 'https://api.siliconflow.cn/v1', testModel = 'Qwen/Qwen2.5-7B-Instruct'): Promise<TokenCheckResult> {
  try {
    // 先检查余额
    const balanceUrl = baseUrl.replace(/\/+$/, '') + '/account/balance';
    const balanceResponse = await fetch(balanceUrl, {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (balanceResponse.ok) {
      const balanceData = await balanceResponse.json();
      const balance = balanceData.data?.balance || -1;
      return { token, isValid: true, balance, message: "有效" };
    }

    // 如果余额检查失败，尝试API调用
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true, balance: -1 };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkDeepSeekToken(token: string, baseUrl = 'https://api.deepseek.com/v1', testModel = 'deepseek-chat'): Promise<TokenCheckResult> {
  try {
    // 先检查余额
    const balanceUrl = baseUrl.replace(/\/+$/, '') + '/user/balance';
    const balanceResponse = await fetch(balanceUrl, {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (balanceResponse.ok) {
      const balanceData = await balanceResponse.json();
      const balance = balanceData.balance_infos?.[0]?.available_balance || -1;
      return { token, isValid: true, balance, message: "有效" };
    }

    // 如果余额检查失败，尝试API调用
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true, balance: -1 };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkMoonshotToken(token: string, baseUrl = 'https://api.moonshot.cn/v1', testModel = 'kimi-latest'): Promise<TokenCheckResult> {
  try {
    // 先检查余额
    const balanceUrl = baseUrl.replace(/\/+$/, '') + '/users/me/balance';
    const balanceResponse = await fetch(balanceUrl, {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (balanceResponse.ok) {
      const balanceData = await balanceResponse.json();
      const balance = balanceData.data?.available_balance || -1;
      return { token, isValid: true, balance, message: "有效" };
    }

    // 如果余额检查失败，尝试API调用
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true, balance: -1 };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkAliyunToken(token: string, baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1', testModel = 'qwen-turbo'): Promise<TokenCheckResult> {
  try {
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkZhipuToken(token: string, baseUrl = 'https://open.bigmodel.cn/api/paas/v4', testModel = 'glm-4.5-air'): Promise<TokenCheckResult> {
  try {
    const apiUrl = baseUrl.replace(/\/+$/, '') + '/chat/completions';
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 1
      })
    });

    if (response.ok) return { token, isValid: true };

    const { message, rawError } = await handleApiError(response);
    return { token, isValid: false, message, rawError, error: true };
  } catch (error) {
    return { token, isValid: false, message: "网络错误", rawError: (error as Error).message, error: true };
  }
}

async function checkKiloToken(token: string): Promise<TokenCheckResult> {
  try {
    const balanceUrl = 'https://kilocode.ai/api/profile/balance';
    
    const response = await fetch(balanceUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Referer': 'https://kilocode.ai/profile'
      }
    });

    if (response.ok) {
      const data = await response.json();
      const balance = data.balance !== undefined ? parseFloat(data.balance) : -1;
      return {
        token: token,
        isValid: true,
        balance: balance,
        message: "有效"
      };
    } else if (response.status === 401) {
      return {
        token: token,
        isValid: false,
        message: "API密钥无效",
        error: true
      };
    } else if (response.status === 404) {
      return {
        token: token,
        isValid: false,
        message: "API端点不存在",
        error: true
      };
    } else {
      const { message, rawError } = await handleApiError(response);
      return {
        token: token,
        isValid: false,
        message: message,
        rawError: rawError,
        error: true
      };
    }
  } catch (error) {
    return {
      token: token,
      isValid: false,
      message: "网络错误",
      rawError: (error as Error).message,
      error: true
    };
  }
}

async function checkNebiusToken(token: string): Promise<TokenCheckResult> {
  try {
    const balanceUrl = 'https://studio.nebius.com/proxy/billing/public/accounts/payments-history';
    
    const response = await fetch(balanceUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      const totalTopUps = parseFloat(data.total_top_ups || '0');
      const totalBonusCredits = parseFloat(data.total_bonus_credits || '0');
      const balance = totalTopUps + totalBonusCredits;
      
      return {
        token: token,
        isValid: true,
        balance: balance,
        totalBalance: balance,
        message: "有效"
      };
    } else if (response.status === 401) {
      return {
        token: token,
        isValid: false,
        message: "API密钥无效",
        error: true
      };
    } else if (response.status === 404) {
      return {
        token: token,
        isValid: false,
        message: "API端点不存在",
        error: true
      };
    } else {
      const { message, rawError } = await handleApiError(response);
      return {
        token: token,
        isValid: false,
        message: message,
        rawError: rawError,
        error: true
      };
    }
  } catch (error) {
    return {
      token: token,
      isValid: false,
      message: "网络错误",
      rawError: (error as Error).message,
      error: true
    };
  }
}

async function checkGMICloudToken(token: string): Promise<TokenCheckResult> {
  try {
    // 简单测试API可用性，不检查余额
    const validationResponse = await fetch('https://api.gmi-serving.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: 'hi' }],
        max_tokens: 1
      })
    });

    if (validationResponse.ok) {
      return {
        token,
        isValid: true,
        message: "有效"
      };
    } else {
      const { message, rawError } = await handleApiError(validationResponse);
      return { token, isValid: false, message, rawError, error: true };
    }
  } catch (error) {
    return {
      token: token,
      isValid: false,
      message: "网络错